'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { X, Calendar, Image, Send } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useContentStore, type SocialPlatform } from '@/stores/contentStore'

interface CreatePostModalProps {
  isOpen: boolean
  onClose: () => void
}

export default function CreatePostModal({ isOpen, onClose }: CreatePostModalProps) {
  const { platforms, addPost } = useContentStore()
  const [content, setContent] = useState('')
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([])
  const [scheduledTime, setScheduledTime] = useState('')
  const [images, setImages] = useState<string[]>([])

  const connectedPlatforms = platforms.filter(p => p.connected)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!content.trim() || selectedPlatforms.length === 0) {
      return
    }

    addPost({
      content,
      platforms: selectedPlatforms,
      scheduledTime: new Date(scheduledTime),
      status: 'scheduled',
      images
    })

    // Reset form
    setContent('')
    setSelectedPlatforms([])
    setScheduledTime('')
    setImages([])
    onClose()
  }

  const togglePlatform = (platformId: string) => {
    setSelectedPlatforms(prev => 
      prev.includes(platformId)
        ? prev.filter(id => id !== platformId)
        : [...prev, platformId]
    )
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="w-full max-w-2xl"
      >
        <Card className="glass-effect border-0 shadow-2xl">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="text-white">إنشاء منشور جديد</CardTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-4 h-4" />
            </Button>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Content */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-white">المحتوى</label>
                <textarea
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder="اكتب محتوى منشورك هنا..."
                  className="w-full h-32 p-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder:text-gray-400 focus:border-blue-400 focus:ring-1 focus:ring-blue-400 resize-none"
                  required
                />
                <div className="text-xs text-gray-400 text-right">
                  {content.length}/280 حرف
                </div>
              </div>

              {/* Platform Selection */}
              <div className="space-y-3">
                <label className="text-sm font-medium text-white">اختر المنصات</label>
                {connectedPlatforms.length === 0 ? (
                  <div className="text-center py-4">
                    <p className="text-gray-400 text-sm">لا توجد منصات متصلة</p>
                    <Button variant="outline" className="mt-2 bg-white/10 border-white/20 text-white">
                      ربط منصة
                    </Button>
                  </div>
                ) : (
                  <div className="grid grid-cols-2 gap-3">
                    {connectedPlatforms.map((platform) => (
                      <button
                        key={platform.id}
                        type="button"
                        onClick={() => togglePlatform(platform.id)}
                        className={`p-3 rounded-lg border transition-all ${
                          selectedPlatforms.includes(platform.id)
                            ? 'border-blue-400 bg-blue-500/20'
                            : 'border-white/20 bg-white/5 hover:bg-white/10'
                        }`}
                      >
                        <div className="flex items-center gap-3">
                          <span className="text-lg">{platform.icon}</span>
                          <span className="text-white text-sm">{platform.name}</span>
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Schedule Time */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-white">وقت النشر</label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    type="datetime-local"
                    value={scheduledTime}
                    onChange={(e) => setScheduledTime(e.target.value)}
                    className="pl-10 bg-white/10 border-white/20 text-white focus:border-blue-400 focus:ring-blue-400"
                    required
                  />
                </div>
              </div>

              {/* Images */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-white">الصور (اختياري)</label>
                <div className="border-2 border-dashed border-white/20 rounded-lg p-6 text-center">
                  <Image className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-400 text-sm">اسحب الصور هنا أو انقر للاختيار</p>
                  <Button variant="outline" className="mt-2 bg-white/10 border-white/20 text-white">
                    اختيار صور
                  </Button>
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  className="flex-1 bg-white/10 border-white/20 text-white hover:bg-white/20"
                >
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  variant="gradient"
                  className="flex-1 gap-2"
                  disabled={!content.trim() || selectedPlatforms.length === 0}
                >
                  <Send className="w-4 h-4" />
                  جدولة المنشور
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
